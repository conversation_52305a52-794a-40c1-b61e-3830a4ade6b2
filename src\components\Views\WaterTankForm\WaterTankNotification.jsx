import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, IconButton, Box, Typography, Slide } from '@material-ui/core';
import { Close as CloseIcon, DoneAll} from '@material-ui/icons';
import { makeStyles } from '@material-ui/core/styles';
import { Alert, AlertTitle } from '@material-ui/lab';

// Definimos transición opcional (desde abajo)
function TransitionUp(props) {
  return <Slide {...props} direction="left" />;
}

const useStyles = makeStyles((theme) => ({
  snackbar: {
    '& .MuiSnackbarContent-root': {
      minWidth: '400px',
      maxWidth: '600px',
    }
  },
  alert: {
    '& .MuiAlert-message': {
      width: '100%',
    }
  },
  alertContent: {
    display: 'flex',
    alignItems: 'flex-start',
    gap: theme.spacing(1),
  },
  iconContainer: {
    display: 'flex',
    alignItems: 'center',
    marginRight: theme.spacing(1),
  },
  messageContainer: {
    flex: 1,
  },
  tankInfo: {
    marginTop: theme.spacing(0.5),
    fontSize: '0.875rem',
    opacity: 0.8,
  },
  actionBadge: {
    display: 'inline-block',
    padding: '2px 8px',
    borderRadius: '12px',
    fontSize: '0.75rem',
    fontWeight: 'bold',
    textTransform: 'uppercase',
    marginLeft: theme.spacing(1),
  },
  fillBadge: {
    backgroundColor: theme.palette.primary.light,
    color: theme.palette.primary.contrastText,
  },
  emptyBadge: {
    backgroundColor: theme.palette.secondary.light,
    color: theme.palette.secondary.contrastText,
  },
}));

/**
 * Componente para mostrar notificaciones de operaciones de tanques de agua
 */
const WaterTankNotification = ({ 
  notification, 
  open, 
  onClose, 
  autoHideDuration = 35000 
}) => {
  const classes = useStyles();

  if (!notification) {
    return null;
  }

  const { type, title, message, tankName, action, timestamp } = notification;

  // Determinar el tipo de alerta basado en el tipo de notificación
  const getAlertSeverity = (type) => {
    switch (type) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
      default:
        return 'info';
    }
  };

  // Obtener el icono apropiado para la acción
  // const getActionIcon = (action) => {
  //   return <DoneAll />;
  // };

  // Obtener el color del badge según la acción
  const getBadgeClass = (action) => {
    switch (action) {
      case 'fill':
        return classes.fillBadge;
      case 'empty':
        return classes.emptyBadge;
      default:
        return classes.fillBadge;
    }
  };

  // Formatear el tiempo transcurrido
  const getTimeAgo = (timestamp) => {
    const now = new Date();
    const notificationTime = new Date(timestamp);
    const diffInSeconds = Math.floor((now - notificationTime) / 1000);

    if (diffInSeconds < 60) {
      return 'Ahora';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `Hace ${minutes} min`;
    } else {
      const hours = Math.floor(diffInSeconds / 3600);
      return `Hace ${hours} h`;
    }
  };

  return (
    <Snackbar
      open={open}
      autoHideDuration={7000}
      onClose={onClose}
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      TransitionComponent={TransitionUp}
      className={classes.snackbar}
    >
      <Alert
        severity={type}
        className={classes.alert}
        action={
          <IconButton
            size="small"
            aria-label="close"
            color="inherit"
            onClick={onClose}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        }
      >
        <AlertTitle>
          <Box display="flex" alignItems="center">
            {title}
            {action && (
              <span className={`${classes.actionBadge} ${getBadgeClass(action)}`}>
                {action === 'fill' ? 'Llenado' : 'Vaciado'}
              </span>
            )}
          </Box>
        </AlertTitle>
        
        <div className={classes.alertContent}>
          {/* <div className={classes.iconContainer}>
            {getActionIcon(action)}
          </div> */}
          
          <div className={classes.messageContainer}>
            <Typography variant="body2">
              {message}
            </Typography>
            
            {tankName && (
              <div className={classes.tankInfo}>
                <strong>Tanque:</strong> {tankName}
                {timestamp && (
                  <span style={{ float: 'right' }}>
                    {getTimeAgo(timestamp)}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </Alert>
    </Snackbar>
  );
};

export default WaterTankNotification;
