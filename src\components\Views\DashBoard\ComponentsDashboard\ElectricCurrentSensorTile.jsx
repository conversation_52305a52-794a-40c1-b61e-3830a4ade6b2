import { Grid, Typography, Divider } from '@material-ui/core'
import React, { useContext, useEffect, useState } from 'react'
import HeadTile from './HeadTile'
import { UserContext } from '../../../../context/UserProvider';
import { db } from '../../../../config/firebase';
import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles({
  divider: {
    margin: '8px 0',
    backgroundColor: '#e0e0e0',
  },
});

export const ElectricCurrentSensorTile = ({ data, style, col }) => {
  const classes = useStyles();
  const uid = data.uid;
  console.log("Esto es data:",data)
  const name = data.name;
  const { usuario } = useContext(UserContext);
  const [allCurrents, setAllCurrents] = useState([])
  const [totalCurrent, setTotalCurrent] = useState(0)
  const [totalVoltage, setTotalVoltage] = useState(0)
  const [totalPower, setTotalPower] = useState(0)

  function sumStringArray(arr) {
    if (!Array.isArray(arr)) return 0;
    return arr.reduce((total, str) => {
      const num = parseFloat(str);
      // Sólo sumamos si num es un número finito
      return !isNaN(num) ? total + num : total;
    }, 0);
  }

  // Función para formatear valores numéricos
  const formatValue = (number, decimals = 2) => {
    if (isNaN(number) || number === null || number === undefined) return "0";
    return Number(number).toFixed(decimals);
  };

  // Listener en tiempo real para datos de corriente
  useEffect(() => {
    if (!usuario.username) return;

    const mac = uid.split("@")[0];
    const canId = uid.split("@")[1];
    const path = `${usuario.username}/infoDevices/${mac}/${canId}/fromModule`;
    const docRef = db.collection(path).doc("togglesInfo");

    const unsubscribe = docRef.onSnapshot((docSnapshot) => {
      if (docSnapshot.exists) {
        const data = docSnapshot.data();
        console.log("Datos de corriente actualizados:", data);

        if (data.currents) {
          const arrayOfCurrents = data.currents;
          setAllCurrents(arrayOfCurrents);
          const totalCurrentValue = sumStringArray(arrayOfCurrents);
          setTotalCurrent(totalCurrentValue);
          console.log("Corriente total actualizada:", totalCurrentValue);
        }
      } else {
        console.log("No se encontró el documento de corrientes");
        setTotalCurrent(0);
        setAllCurrents([]);
      }
    }, (error) => {
      console.error("Error al escuchar cambios en corrientes:", error);
    });

    return () => {
      unsubscribe();
    };
  }, [usuario.username, uid]);

  // Listener en tiempo real para datos de voltaje
  useEffect(() => {
    if (!usuario.username) return;

    const mac = uid.split("@")[0];
    const canId = uid.split("@")[1];
    const path = `${usuario.username}/infoDevices/${mac}/${canId}/configModule`;
    const docRef = db.collection(path).doc(uid);

    const unsubscribe = docRef.onSnapshot((docSnapshot) => {
      if (docSnapshot.exists) {
        const data = docSnapshot.data();
        const item = data.item;
        console.log("Datos de voltaje actualizados:", data);

        if (item &&item.totalVoltage !== undefined) {
          setTotalVoltage(item.totalVoltage);
          console.log("Voltaje total actualizado:", item.totalVoltage);
        }
      } else {
        console.log("No se encontró el documento de tensión");
        setTotalVoltage(0);
      }
    }, (error) => {
      console.error("Error al escuchar cambios en voltaje:", error);
    });

    return () => {
      unsubscribe();
    };
  }, [usuario.username, uid]);

  // Calcular potencia cuando cambien corriente o voltaje
  useEffect(() => {
    const powerValue = totalCurrent * totalVoltage;
    setTotalPower(powerValue);
    console.log("Potencia total calculada:", powerValue);
  }, [totalCurrent, totalVoltage]);
  

  return (
    <Grid
      container
      direction="row"
      justifyContent="center"
      alignItems="center"
      style={style}
    >
      <HeadTile name={name} uid={uid} col={col} />

      {/* Valor de Amperaje */}
      <Grid
        item
        xs={6}
        container
        direction="column"
        alignItems="center"
        justifyContent="center"
        className="parameter-grid"
      >
        <Typography variant="subtitle2" gutterBottom>
          AMP
        </Typography>
        <h5>
          <span className="badge badge-dark value-badge">
            {formatValue(totalCurrent, 2)}
          </span>
        </h5>
      </Grid>

      {/* Valor de Voltaje */}
      <Grid
        item
        xs={6}
        container
        direction="column"
        alignItems="center"
        justifyContent="center"
        className="parameter-grid"
      >
        <Typography variant="subtitle2" gutterBottom>
          VOLT
        </Typography>
        <h5>
          <span className="badge badge-dark value-badge">
            {formatValue(totalVoltage, 1)}
          </span>
        </h5>
      </Grid>

      {/* Divider */}
      <Grid item xs={11}>
        <Divider className={classes.divider} />
      </Grid>

      {/* Valor de Potencia */}
      <Grid
        item
        xs={6}
        container
        direction="column"
        alignItems="center"
        justifyContent="center"
        className="parameter-grid"
      >
        <Typography variant="subtitle2" gutterBottom>
          WATT
        </Typography>
        <h5>
          <span className="badge badge-dark value-badge">
            {formatValue(totalPower, 2)}
          </span>
        </h5>
      </Grid>
    </Grid>
  )
}
