import { useState, useCallback, useEffect } from 'react';

const STORAGE_KEY = 'waterTankNotifications';
const MAX_NOTIFICATIONS = 50; // Límite máximo de notificaciones almacenadas

/**
 * Hook personalizado para manejar notificaciones de tanques de agua
 * Proporciona funcionalidades para mostrar notificaciones cuando las operaciones terminan
 * Incluye persistencia local para mantener el historial
 */
const useWaterTankNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [currentNotification, setCurrentNotification] = useState(null);

  // Cargar notificaciones desde localStorage al inicializar
  useEffect(() => {
    try {
      const savedNotifications = localStorage.getItem(STORAGE_KEY);
      if (savedNotifications) {
        const parsed = JSON.parse(savedNotifications);
        setNotifications(parsed);
      }
    } catch (error) {
      console.error('[useWaterTankNotifications] Error al cargar notificaciones:', error);
    }
  }, []);

  // Guardar notificaciones en localStorage cuando cambien
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(notifications));
    } catch (error) {
      console.error('[useWaterTankNotifications] Error al guardar notificaciones:', error);
    }
  }, [notifications]);

  // Función para agregar una nueva notificación
  const addNotification = useCallback((notification) => {
    const newNotification = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      ...notification
    };

    console.log('[useWaterTankNotifications] Nueva notificación:', newNotification);

    // Agregar a la lista de notificaciones (mantener límite máximo)
    setNotifications(prev => {
      const updated = [newNotification, ...prev];
      return updated.slice(0, MAX_NOTIFICATIONS);
    });

    // Mostrar como notificación actual
    setCurrentNotification(newNotification);

    // Auto-ocultar después de 6 segundos
    setTimeout(() => {
      setCurrentNotification(null);
    }, 6000);

    return newNotification.id;
  }, []);

  // Función para remover una notificación específica
  const removeNotification = useCallback((notificationId) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
    
    // Si es la notificación actual, ocultarla
    if (currentNotification && currentNotification.id === notificationId) {
      setCurrentNotification(null);
    }
  }, [currentNotification]);

  // Función para limpiar todas las notificaciones
  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
    setCurrentNotification(null);
  }, []);

  // Función para ocultar la notificación actual
  const hideCurrentNotification = useCallback(() => {
    setCurrentNotification(null);
  }, []);

  // Función específica para notificaciones de tanques de agua
  const notifyWaterTankOperation = useCallback((operationData) => {
    const { type, title, message, tankId, tankName, action } = operationData;
    
    return addNotification({
      type: type || 'info',
      title: title || 'Operación de Tanque',
      message: message || 'Operación completada',
      category: 'water-tank',
      tankId,
      tankName,
      action,
      autoHide: true
    });
  }, [addNotification]);

  // Función para obtener notificaciones por categoría
  const getNotificationsByCategory = useCallback((category) => {
    return notifications.filter(n => n.category === category);
  }, [notifications]);

  // Función para obtener notificaciones de tanques de agua
  const getWaterTankNotifications = useCallback(() => {
    return getNotificationsByCategory('water-tank');
  }, [getNotificationsByCategory]);

  return {
    // Estado
    notifications,
    currentNotification,
    
    // Funciones generales
    addNotification,
    removeNotification,
    clearAllNotifications,
    hideCurrentNotification,
    
    // Funciones específicas para tanques de agua
    notifyWaterTankOperation,
    getWaterTankNotifications,
    
    // Utilidades
    getNotificationsByCategory,
    hasNotifications: notifications.length > 0,
    hasCurrentNotification: currentNotification !== null
  };
};

export default useWaterTankNotifications;
